/* eslint-disable */
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Track, VideoPreset } from "livekit-client";
import { Popover, Modal, Tabs, Switch } from "antd";
import { onDeviceError } from "../../utils/helper";
import { TrackToggle } from "../TrackToggle";
import { ReactComponent as ScreenShareIcon } from "../../assets/icons/Share.svg";
import DefaultScreenImg from "./Assets/DefaultScreen.png";

import "../../styles/ControlBar.scss";
import "../../styles/index.scss";
import "./ScreenShareMenuButton.scss";

export function ScreenShareMenuButton({
  onScreenShareChange,
  maxWidth,
  maxHeight,
  roomData,
  setScreenShareMode,
  meetingFeatures,
  setIsPIPEnabled,
  screenShareSources,
  isElectronApp,
  room,
  setScreenShareDisplayId,
  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  const [showPopover, setShowPopover] = useState(false);
  const [screenSourceId, setScreenSourceId] = useState(null);
  const [activeTabKey, setActiveTabKey] = useState("1");
  const [showScreenShareModal, setShowScreenShareModal] = useState(false);
  
  // OPTIMIZATION: Memoize screen sources data to prevent recreation
  const [screensScourcesData, setScreensScourcesData] = useState([
    {
      id: "1",
      label: "Screen",
      sources: [],
    },
    {
      id: "2",
      label: "Application window",
      sources: [],
    },
  ]);

  // OPTIMIZATION: Memoize mode change handler
  const handleChangeMode = useCallback((mode) => {
    setScreenShareMode(mode);

    // Check if roomData and its options exist before trying to modify them
    if (roomData && roomData.options && roomData.options.publishDefaults) {
      if (mode === "video") {
        roomData.options.publishDefaults.screenShareEncoding = {
          maxBitrate: 1_800_000,
          maxFramerate: 30,
        };
        roomData.options.publishDefaults.screenShareSimulcastLayers = [
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 800_000,
              maxFramerate: 5,
            },
          },
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 400_000,
              maxFramerate: 1,
            },
          },
        ];
      } else if (mode === "text") {
        roomData.options.publishDefaults.screenShareEncoding = {
          maxBitrate: 1_000_000,
          maxFramerate: 5,
        };
        roomData.options.publishDefaults.screenShareSimulcastLayers = [
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 800_000,
              maxFramerate: 1,
            },
          },
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 400_000,
              maxFramerate: 1,
            },
          },
        ];
      }
    } else {
      console.warn('Room options not available for screen share configuration');
    }
    setIsPIPEnabled(true);
  }, [setScreenShareMode, roomData, maxWidth, maxHeight, setIsPIPEnabled]);

  // OPTIMIZATION: Memoize capture options to prevent recreation
  const textCaptureOptions = useMemo(() => ({
    audio: true,
    contentHint: "detail",
    resolution: {
      width: maxWidth,
      height: maxHeight,
      frameRate: 5,
    },
    selfBrowserSurface: "include",
  }), [maxWidth, maxHeight]);

  const videoCaptureOptions = useMemo(() => ({
    audio: true,
    contentHint: "detail",
    resolution: {
      width: maxWidth,
      height: maxHeight,
      frameRate: 30,
    },
    selfBrowserSurface: "include",
  }), [maxWidth, maxHeight]);

  // OPTIMIZATION: Memoize content to prevent recreation
  const content = useMemo(() => (
    <div className="primary-font ss-parent">
      <TrackToggle
        source={Track.Source.ScreenShare}
        captureOptions={textCaptureOptions}
        showIcon={false}
        onChange={onScreenShareChange}
        className="ss-button"
        onDeviceError={onDeviceError}
        onClick={() => handleChangeMode("text")}
      >
        Screen
      </TrackToggle>
      <TrackToggle
        source={Track.Source.ScreenShare}
        captureOptions={videoCaptureOptions}
        showIcon={false}
        onChange={onScreenShareChange}
        className="ss-button"
        onDeviceError={onDeviceError}
        onClick={() => handleChangeMode("video")}
      >
        Video
      </TrackToggle>
    </div>
  ), [textCaptureOptions, videoCaptureOptions, onScreenShareChange, handleChangeMode]);

  // OPTIMIZATION: Memoize screen sources update to prevent unnecessary processing
  useEffect(() => {
    if (screenShareSources.length > 0) {
      const updatedData = screensScourcesData.map((item) => ({
        ...item,
        sources:
          item.label === "Screen"
            ? screenShareSources.filter((source) =>
                source.id.includes("screen")
              )
            : screenShareSources.filter(
                (source) => !source.id.includes("screen")
              ),
      }));

      setScreensScourcesData(updatedData);

      // Set default source ID to the first source in the initial tab
      if (updatedData[0].sources.length > 0 && screenSourceId === null) {
        setScreenSourceId(updatedData[0].sources[0].id);
      }
    }
  }, [screenShareSources, screenSourceId]);

  // OPTIMIZATION: Memoize app screen share handler
  const handleAppScreenShare = useCallback(async () => {
    if (screenShareSources.length > 0) {
      if (screenSourceId !== null) {
        let stream;

        try {
          // Request screen capture
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              mandatory: {
                chromeMediaSource: "desktop",
                chromeMediaSourceId: screenSourceId,
                maxWidth: 1920,
                maxHeight: 1080,
              },
            },
            audio: false
          });
        } catch (error) {
          setToastNotification(error.message);
          setToastStatus("error");
          setShowToast(true);
          return;
        }

        // Check if stream was successfully obtained
        if (stream) {
          // Get the video and audio tracks from the stream
          const track = stream.getVideoTracks()[0];
          if (!track) {
            console.error("No video track found");
            return;
          }

          const { width, height } = track.getSettings();

          try {
            // Publish the track with simulcast options
            await room.localParticipant.publishTrack(track, {
              simulcast: true,
              screenShareSimulcastLayers: [
                new VideoPreset(width || 0, height || 0, 500000, 5),
                new VideoPreset(width || 0, height || 0, 200000, 1),
              ],
              source: Track.Source.ScreenShare,
            });
            setShowToast(true);
            setToastNotification("Screen sharing started successfully!");
            setToastStatus("success");
          } catch (publishError) {
            console.error("Error publishing track:", publishError);
          }
        }
      }
      onScreenShareChange(true);
    } else {
      setToastNotification("No screen share sources available.");
      setToastStatus("warning");
      setShowToast(true);
    }
  }, [screenShareSources, screenSourceId, room, onScreenShareChange, setToastNotification, setToastStatus, setShowToast]);

  // OPTIMIZATION: Memoize source selection handler
  const handleSelectSource = useCallback((source) => {
    if (source.displayId && source.displayId !== "") {
      const numericDisplayId = Number(source.displayId);
      if (!Number.isNaN(numericDisplayId)) {
        setScreenShareDisplayId(numericDisplayId);
      }
    } else {
      setScreenShareDisplayId(1);
    }
    setScreenSourceId(source.id);
  }, [setScreenShareDisplayId]);

  // OPTIMIZATION: Memoize tab change handler
  const handleTabChange = useCallback((key) => {
    setActiveTabKey(key);
    const newActiveTabSources = screensScourcesData.find(
      (tab) => tab.id === key
    ).sources;

    if (newActiveTabSources.length > 0) {
      setScreenSourceId(newActiveTabSources[0].id);
    } else {
      setScreenSourceId(null);
    }
  }, [screensScourcesData]);

  // OPTIMIZATION: Memoize helper function
  const toBase64 = useCallback((arrayBuffer) => {
    let binary = "";
    const bytes = new Uint8Array(arrayBuffer);
    for (let i = 0; i < bytes.length; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }, []);

  // OPTIMIZATION: Memoize tab items to prevent recreation
  const tabItems = useMemo(() => screensScourcesData.map((tab) => ({
    key: tab.id,
    label: tab.label,
    children:
      tab.sources.length > 0 &&
      tab.sources.map((sourceItem) => (
        <div
          className={`${
            screenSourceId === sourceItem.id
              ? "selected-screen-share d-flex"
              : ""
          } screen-share-option`}
          onClick={() => handleSelectSource(sourceItem)}
          key={sourceItem.id}
        >
          {sourceItem.thumb.length > 0 ? (
            <img
              src={`data:image/png;base64,${toBase64(sourceItem.thumb)}`}
              alt={sourceItem.name}
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = DefaultScreenImg;
              }}
            />
          ) : (
            <img src={DefaultScreenImg} alt="Default Screen" />
          )}
          <p>
            {sourceItem.name.length > 15
              ? `${sourceItem.name.substring(0, 15)}...`
              : sourceItem.name}
          </p>
        </div>
      )),
  })), [screensScourcesData, screenSourceId, handleSelectSource, toBase64]);

  // OPTIMIZATION: Memoize modal handlers
  const showModal = useCallback(() => {
    setShowScreenShareModal(true);
  }, []);

  const handleOk = useCallback(() => {
    handleAppScreenShare();
    setShowScreenShareModal(false);
  }, [handleAppScreenShare]);

  const handleCancel = useCallback(() => {
    setShowScreenShareModal(false);
  }, []);

  // OPTIMIZATION: Memoize electron content
  const contentElectron = useMemo(() => (
    <>
      <div className="primary-font ss-parent">
        <div
          className="ss-button"
          onClick={() => {
            showModal();
            setShowPopover(false);
          }}
        >
          Screen
        </div>
        <div
          className="ss-button"
          onClick={() => {
            showModal();
            setShowPopover(false);
          }}
        >
          Video
        </div>
      </div>
    </>
  ), [showModal]);

  return (
    <>
      {meetingFeatures?.share_youtube === 1 ? (
        <Popover
          content={isElectronApp ? contentElectron : content}
          title={null}
          trigger="click"
          open={showPopover}
          onOpenChange={() => setShowPopover(!showPopover)}
          overlayInnerStyle={{
            borderRadius: "10px",
          }}
          overlayClassName="screen-share-popover"
        >
          <div className="lk-button control-bar-button control-bar-button-icon lk-button-group-buttons">
            <ScreenShareIcon />
          </div>
        </Popover>
      ) : isElectronApp ? (
        <div
          className="lk-button control-bar-button control-bar-button-icon lk-button-group-buttons"
          onClick={showModal}
        >
          <ScreenShareIcon />
        </div>
      ) : (
        <TrackToggle
          source={Track.Source.ScreenShare}
          captureOptions={textCaptureOptions}
          showIcon={false}
          onChange={onScreenShareChange}
          className="lk-button control-bar-button control-bar-button-icon lk-button-group-buttons"
          onDeviceError={onDeviceError}
          onClick={() => handleChangeMode("text")}
        >
          <ScreenShareIcon />
        </TrackToggle>
      )}

      {showScreenShareModal && (
        <Modal
          title="Screen Share"
          open={showScreenShareModal}
          onOk={handleOk}
          onCancel={handleCancel}
          okText="Start Sharing"
          cancelText="Cancel"
          className="screen-share-modal"
        >
          <Tabs
            items={tabItems}
            activeKey={activeTabKey}
            onChange={handleTabChange}
            className={`screen-share-tabs`}
          />
        </Modal>
      )}
    </>
  );
}
